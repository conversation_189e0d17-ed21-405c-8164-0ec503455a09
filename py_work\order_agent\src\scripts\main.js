/**
 * 订单录入智能体系统 - 主应用程序
 * 
 * 这是应用程序的主入口文件，负责：
 * - 应用程序初始化
 * - 模块加载和依赖管理
 * - 全局状态管理
 * - 路由管理
 * - 错误处理
 */

// ==========================================================================
// 应用程序配置
// ==========================================================================

const APP_CONFIG = {
  name: 'Order Agent',
  version: '1.0.0',
  debug: true,
  apiBaseUrl: '/api',
  defaultPage: 'login',
  pages: ['login', 'dashboard', 'order-review', 'analytics'],
  loadTimeout: 5000
};

// ==========================================================================
// 全局状态管理
// ==========================================================================

class AppState {
  constructor() {
    this.state = {
      currentUser: null,
      currentPage: APP_CONFIG.defaultPage,
      isLoading: false,
      isAuthenticated: false,
      notifications: [],
      theme: 'light'
    };
    
    this.listeners = new Map();
  }

  // 获取状态
  get(key) {
    return this.state[key];
  }

  // 设置状态
  set(key, value) {
    const oldValue = this.state[key];
    this.state[key] = value;
    
    // 触发状态变化监听器
    if (this.listeners.has(key)) {
      this.listeners.get(key).forEach(callback => {
        callback(value, oldValue);
      });
    }
  }

  // 监听状态变化
  subscribe(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, []);
    }
    this.listeners.get(key).push(callback);
  }

  // 取消监听
  unsubscribe(key, callback) {
    if (this.listeners.has(key)) {
      const callbacks = this.listeners.get(key);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }
}

// ==========================================================================
// 应用程序类
// ==========================================================================

class OrderAgentApp {
  constructor() {
    this.state = new AppState();
    this.modules = new Map();
    this.isInitialized = false;
    
    // 绑定方法上下文
    this.handleError = this.handleError.bind(this);
    this.showLoading = this.showLoading.bind(this);
    this.hideLoading = this.hideLoading.bind(this);
  }

  // 初始化应用程序
  async init() {
    try {
      console.log(`🚀 初始化 ${APP_CONFIG.name} v${APP_CONFIG.version}`);
      
      this.showLoading();
      
      // 设置全局错误处理
      this.setupErrorHandling();
      
      // 初始化DOM
      await this.initializeDOM();
      
      // 加载核心模块
      await this.loadCoreModules();
      
      // 设置路由
      this.setupRouting();
      
      // 恢复用户会话
      await this.restoreSession();
      
      // 初始化页面
      await this.initializePage();
      
      this.isInitialized = true;
      this.hideLoading();
      
      console.log('✅ 应用程序初始化完成');
      
    } catch (error) {
      this.handleError('应用程序初始化失败', error);
    }
  }

  // 设置错误处理
  setupErrorHandling() {
    window.addEventListener('error', (event) => {
      this.handleError('JavaScript错误', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.handleError('未处理的Promise拒绝', event.reason);
    });
  }

  // 初始化DOM
  async initializeDOM() {
    // 等待DOM完全加载
    if (document.readyState === 'loading') {
      await new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', resolve);
      });
    }

    // 初始化Bootstrap组件
    this.initializeBootstrap();
  }

  // 初始化Bootstrap组件
  initializeBootstrap() {
    // 初始化所有tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

    // 初始化所有popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl));
  }

  // 加载核心模块
  async loadCoreModules() {
    const coreModules = [
      { name: 'auth', path: './auth.js' },
      { name: 'router', path: './router.js' },
      { name: 'api', path: './api.js' },
      { name: 'utils', path: './utils.js' },
      { name: 'notifications', path: './notifications.js' }
    ];

    for (const moduleInfo of coreModules) {
      try {
        const module = await import(moduleInfo.path);
        this.modules.set(moduleInfo.name, module.default || module);
        console.log(`📦 已加载模块: ${moduleInfo.name}`);
      } catch (error) {
        console.warn(`⚠️ 模块加载失败: ${moduleInfo.name}`, error);
      }
    }
  }

  // 设置路由
  setupRouting() {
    const router = this.modules.get('router');
    if (router) {
      router.init(this);
    }
  }

  // 恢复用户会话
  async restoreSession() {
    const auth = this.modules.get('auth');
    if (auth) {
      const user = await auth.getCurrentUser();
      if (user) {
        this.state.set('currentUser', user);
        this.state.set('isAuthenticated', true);
        this.state.set('currentPage', 'dashboard');
      }
    }
  }

  // 初始化页面
  async initializePage() {
    const currentPage = this.state.get('currentPage');
    await this.navigateToPage(currentPage);
  }

  // 页面导航
  async navigateToPage(pageName) {
    if (!APP_CONFIG.pages.includes(pageName)) {
      console.error(`❌ 未知页面: ${pageName}`);
      return;
    }

    try {
      this.showLoading();
      
      // 加载页面模块
      const pageModule = await this.loadPageModule(pageName);
      
      // 更新页面状态
      this.state.set('currentPage', pageName);
      
      // 显示页面
      this.showPage(pageName);
      
      // 初始化页面
      if (pageModule && pageModule.init) {
        await pageModule.init(this);
      }
      
      this.hideLoading();
      
    } catch (error) {
      this.handleError(`页面导航失败: ${pageName}`, error);
    }
  }

  // 加载页面模块
  async loadPageModule(pageName) {
    const moduleKey = `page-${pageName}`;
    
    if (this.modules.has(moduleKey)) {
      return this.modules.get(moduleKey);
    }

    try {
      const module = await import(`./pages/${pageName}.js`);
      this.modules.set(moduleKey, module.default || module);
      return this.modules.get(moduleKey);
    } catch (error) {
      console.warn(`⚠️ 页面模块加载失败: ${pageName}`, error);
      return null;
    }
  }

  // 显示页面
  showPage(pageName) {
    // 隐藏所有页面
    document.querySelectorAll('.page-content').forEach(page => {
      page.classList.remove('active');
    });

    // 显示目标页面
    const targetPage = document.getElementById(`${pageName}Page`);
    if (targetPage) {
      targetPage.classList.add('active');
    }

    // 显示/隐藏主应用区域
    const mainApp = document.getElementById('mainApp');
    if (pageName === 'login') {
      mainApp.classList.remove('active');
    } else {
      mainApp.classList.add('active');
    }
  }

  // 显示加载状态
  showLoading() {
    this.state.set('isLoading', true);
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
      spinner.classList.remove('hidden');
    }
  }

  // 隐藏加载状态
  hideLoading() {
    this.state.set('isLoading', false);
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
      spinner.classList.add('hidden');
    }
  }

  // 错误处理
  handleError(message, error) {
    console.error(`❌ ${message}:`, error);
    
    this.hideLoading();
    
    // 显示错误通知
    const notifications = this.modules.get('notifications');
    if (notifications) {
      notifications.showError(message);
    } else {
      alert(`错误: ${message}`);
    }
  }

  // 获取模块
  getModule(name) {
    return this.modules.get(name);
  }
}

// ==========================================================================
// 应用程序启动
// ==========================================================================

// 创建全局应用实例
window.OrderAgentApp = new OrderAgentApp();

// 启动应用程序
window.OrderAgentApp.init().catch(error => {
  console.error('💥 应用程序启动失败:', error);
});

// 导出应用实例（用于模块间通信）
export default window.OrderAgentApp;
