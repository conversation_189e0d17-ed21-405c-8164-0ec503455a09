{"name": "order-agent", "version": "1.0.0", "description": "订单录入智能体系统 - 前端界面", "main": "src/scripts/main.js", "scripts": {"dev": "live-server src --port=3000 --open=/index.html", "build": "npm run build:css && npm run build:js && npm run build:html", "build:css": "postcss src/styles/main.css -o dist/css/main.min.css --use autoprefixer cssnano", "build:js": "terser src/scripts/main.js -o dist/js/main.min.js --compress --mangle", "build:html": "html-minifier --input-dir src --output-dir dist --file-ext html --remove-comments --collapse-whitespace", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/scripts/**/*.js", "format": "prettier --write src/**/*.{html,css,js}"}, "keywords": ["order-management", "ai-agent", "logistics", "frontend"], "author": "Order Agent Team", "license": "MIT", "devDependencies": {"live-server": "^1.2.2", "postcss": "^8.4.31", "postcss-cli": "^10.1.0", "autoprefixer": "^10.4.16", "cssnano": "^6.0.1", "terser": "^5.24.0", "html-minifier": "^4.0.0", "eslint": "^8.54.0", "prettier": "^3.1.0", "rimraf": "^5.0.5"}, "dependencies": {"bootstrap": "^5.3.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}