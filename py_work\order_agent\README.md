# 订单录入智能体系统

一个基于AI的订单处理和管理系统前端界面，支持邮件解析、智能表单填写、订单审核和数据分析。

## 🚀 功能特性

- **智能登录系统** - 用户身份验证和权限管理
- **订单任务仪表盘** - 实时统计、任务列表、线路聚合报表
- **AI订单审核** - 邮件解析、智能表单、AI助手对话
- **数据分析报表** - KPI展示、综合查询、数据导出
- **响应式设计** - 支持桌面端和移动端访问

## 🛠️ 技术栈

- **前端框架**: 原生HTML5 + CSS3 + JavaScript ES6+
- **UI组件库**: Bootstrap 5.3.2
- **图标库**: Font Awesome 6.4.2
- **构建工具**: PostCSS, Terser, HTML Minifier
- **开发服务器**: Live Server

## 📁 项目结构

```
order-agent/
├── src/                 # 源代码目录
│   ├── index.html      # 主入口文件
│   ├── pages/          # 页面组件
│   ├── components/     # 可复用组件
│   ├── styles/         # 样式文件
│   ├── scripts/        # JavaScript模块
│   └── assets/         # 静态资源
├── dist/               # 构建输出目录
├── package.json        # 项目配置
└── README.md          # 项目说明
```

## 🚦 快速开始

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```
访问 http://localhost:3000 查看应用

### 构建生产版本
```bash
npm run build
```

### 代码格式化
```bash
npm run format
```

### 代码检查
```bash
npm run lint
```

## 📱 页面说明

1. **登录页面** (`/`) - 用户身份验证
2. **任务仪表盘** (`/dashboard`) - 订单任务管理
3. **订单审核** (`/order-review`) - AI辅助订单处理
4. **分析报表** (`/analytics`) - 数据统计和分析

## 🎨 设计规范

- 采用Bootstrap设计系统
- 支持深色/浅色主题切换
- 遵循Material Design交互规范
- 响应式断点: 576px, 768px, 992px, 1200px

## 🔧 开发指南

### 添加新页面
1. 在 `src/pages/` 创建HTML文件
2. 在 `src/styles/pages/` 添加对应样式
3. 在 `src/scripts/` 添加页面逻辑
4. 更新路由配置

### 创建组件
1. 在 `src/components/` 创建HTML模板
2. 在 `src/styles/components/` 添加组件样式
3. 在 `src/scripts/components/` 添加组件逻辑

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件
