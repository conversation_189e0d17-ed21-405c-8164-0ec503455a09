/* ==========================================================================
   登录页面样式
   ========================================================================== */

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.login-card {
  width: 100%;
  max-width: 420px;
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  position: relative;
  z-index: 1;
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-card .card-body {
  padding: 2.5rem;
}

.login-brand {
  text-align: center;
  margin-bottom: 2rem;
}

.login-brand .brand-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), #4c84ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  box-shadow: 0 10px 20px rgba(13, 110, 253, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 10px 20px rgba(13, 110, 253, 0.3);
  }
  50% {
    box-shadow: 0 10px 30px rgba(13, 110, 253, 0.5);
  }
  100% {
    box-shadow: 0 10px 20px rgba(13, 110, 253, 0.3);
  }
}

.login-brand .brand-icon i {
  font-size: 2.5rem;
  color: white;
}

.login-brand h3 {
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.login-brand p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0;
}

.login-form .form-floating {
  margin-bottom: 1.5rem;
}

.login-form .form-control {
  border: 2px solid #e9ecef;
  border-radius: var(--border-radius-md);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all var(--transition-normal);
}

.login-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

.login-form .form-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.login-btn {
  width: 100%;
  padding: 0.875rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  background: linear-gradient(135deg, var(--primary-color), #4c84ff);
  border: none;
  color: white;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(13, 110, 253, 0.3);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn.loading {
  pointer-events: none;
}

.login-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.login-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e9ecef;
}

.login-footer p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0;
}

.login-footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.login-footer a:hover {
  text-decoration: underline;
}

/* 错误提示样式 */
.login-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: var(--border-radius-md);
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  display: none;
}

.login-error.show {
  display: block;
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 576px) {
  .login-container {
    padding: 1rem;
  }
  
  .login-card .card-body {
    padding: 2rem 1.5rem;
  }
  
  .login-brand .brand-icon {
    width: 60px;
    height: 60px;
  }
  
  .login-brand .brand-icon i {
    font-size: 2rem;
  }
  
  .login-brand h3 {
    font-size: 1.5rem;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(33, 37, 41, 0.95);
    color: white;
  }
  
  .login-brand h3 {
    color: white;
  }
  
  .login-form .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: white;
  }
  
  .login-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
  
  .login-form .form-label {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .login-footer {
    border-top-color: rgba(255, 255, 255, 0.2);
  }
}
