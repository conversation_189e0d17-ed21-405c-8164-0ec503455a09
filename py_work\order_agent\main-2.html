<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单录入智能体 - Uniner Agent</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        body { background-color: #f8f9fa; }
        
        /* 登录页面样式 */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            width: 100%;
            max-width: 400px;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }
        
        /* 通用样式 */
        .stat-card .card-body { display: flex; align-items: center; }
        .stat-card i { font-size: 2rem; margin-right: 1rem; }
        .table-hover tbody tr { cursor: pointer; }
        .confidence-cell { display: flex; align-items: center; }
        .risk-icon { margin-left: 8px; color: #dc3545; font-size: 1.1em; }
        .nav-tabs .nav-link { color: #6c757d; }
        .nav-tabs .nav-link.active { color: #0d6efd; font-weight: 600; }
        .kpi-card .display-4 { font-weight: 700; }
        .chart-placeholder {
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e9ecef;
            color: #6c757d;
            border-radius: .375rem;
        }
        
        /* 订单审核页面样式 */
        .review-container { display: flex; flex-direction: column; min-height: calc(100vh - 56px); }
        .review-content { flex-grow: 1; overflow-y: auto; }
        .card-scrollable { height: 100%; display: flex; flex-direction: column; }
        .card-body-scrollable { flex-grow: 1; overflow-y: auto; position: relative; }
        
        /* 响应式布局调整 */
        @media (max-width: 991.98px) {
            .review-container { min-height: auto; }
            .review-content { overflow-y: visible; }
            .card-scrollable { min-height: 300px; height: auto; }
            .card-body-scrollable { max-height: 400px; }
        }
        
        @media (max-width: 767.98px) {
            .review-container { min-height: auto; }
            .review-content .row { flex-direction: column; }
            .card-scrollable { min-height: 250px; height: auto; }
            .card-body-scrollable { max-height: 300px; }
            .chatbot-container { border-top: 1px solid #dee2e6; margin-top: 1rem; }
            .chat-history { height: 200px; }
        }
        
        @media (max-width: 575.98px) {
            .review-content { padding: 1rem !important; }
            .card-scrollable { min-height: 200px; }
            .card-body-scrollable { max-height: 250px; }
            .chat-history { height: 150px; }
            
            /* 优化表单布局 */
            .form-section-title { font-size: 1rem; margin-top: 0.5rem; }
            .row.g-3 .col-md-6, .row.g-3 .col-md-4, .row.g-3 .col-md-2 {
                margin-bottom: 1rem;
            }
            
            /* 优化聊天区域 */
            .chat-message .message-content { max-width: 90%; }
            .ai-message .btn-group .btn { font-size: 0.75em; padding: 0.25rem 0.5rem; }
        }
        
        /* 通用移动端优化 */
        @media (max-width: 767.98px) {
            .accordion-button { font-size: 0.9rem; padding: 0.75rem; }
            .log-item { margin-bottom: 0.75rem; }
            .log-icon { font-size: 1rem; width: 25px; }
            .stat-card i { font-size: 1.5rem; }
            
            /* 优化面包屑导航 */
            .breadcrumb-item { font-size: 0.9rem; }
        }
        .form-section-title { font-size: 1.1rem; font-weight: 600; color: #0d6efd; margin-top: 1rem; margin-bottom: 0.5rem; border-bottom: 2px solid #0d6efd; padding-bottom: 5px;}
        .highlight-warning { border-color: #ffc107 !important; box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25); }
        .ai-tip { font-size: 0.875em; color: #6c757d; }
        .field-confidence { display: flex; align-items: center; gap: 8px; }
        .confidence-badge { font-size: 0.75rem; padding: 2px 6px; border-radius: 10px; font-weight: 600; }
        .confidence-high { background-color: #d4edda; color: #155724; }
        .confidence-medium { background-color: #fff3cd; color: #856404; }
        .confidence-low { background-color: #f8d7da; color: #721c24; }
        .confidence-missing { background-color: #e2e3e5; color: #383d41; }
        .log-item { display: flex; align-items: flex-start; margin-bottom: 1rem; }
        .log-icon { font-size: 1.2rem; color: #6c757d; width: 30px; text-align: center; margin-top: 5px; }
        .log-content { flex-grow: 1; margin-left: 1rem; }
        .log-title { font-weight: 600; }
        .log-detail { font-size: 0.9em; color: #6c757d; }
        .chatbot-container { border-top: 1px solid #dee2e6; padding: 1rem; background-color: #ffffff; }
        .chat-history { height: 250px; overflow-y: auto; margin-bottom: 1rem; padding-right: 10px; }
        
        /* 确保两栏高度对齐 */
        .review-content .row { align-items: stretch; }
        .card-scrollable { display: flex; flex-direction: column; }
        .card-body-scrollable { flex: 1; }
        .chatbot-container { flex-shrink: 0; }
        
        /* 左右两栏高度对齐优化 */
        .review-content .col-lg-5, .review-content .col-lg-7 { display: flex; flex-direction: column; }
        .review-content .col-lg-5 .card-scrollable, .review-content .col-lg-7 .card-scrollable { height: 100%; }
        .review-content .col-lg-7 .chatbot-container { margin-top: auto; }
        .chat-message { display: flex; margin-bottom: 1rem; }
        .chat-message .avatar { width: 30px; height: 30px; border-radius: 50%; background-color: #e9ecef; display: flex; align-items: center; justify-content: center; font-size: 0.8rem;}
        .chat-message .message-content { margin-left: 10px; padding: 0.5rem 1rem; border-radius: 1rem; max-width: 80%; }
        .user-message { justify-content: flex-end; }
        .user-message .message-content { background-color: #0d6efd; color: white; }
        .ai-message .message-content { background-color: #e9ecef; }
        .ai-message .btn-group .btn { font-size: 0.8em; }
        
        /* 页面切换 */
        .page-content { display: none; }
        .page-content.active { display: block; }
        
        /* 主应用区域 */
        .main-app { display: none; }
        .main-app.active { display: block; }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="page-content active">
        <div class="login-container">
            <div class="card login-card">
                <div class="card-body p-4 p-md-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-robot fa-3x text-primary"></i>
                        <h3 class="mt-3 fw-bold">订单录入智能体</h3>
                        <p class="text-muted">Uniner Agent</p>
                    </div>
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱账号</label>
                            <input type="email" class="form-control form-control-lg" id="email" placeholder="请输入您的邮箱" required>
                        </div>
                        <div class="mb-4">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control form-control-lg" id="password" placeholder="请输入您的密码" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">登 录</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 主应用区域 -->
    <div id="mainApp" class="main-app">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#"><i class="fas fa-robot"></i> Uniner Agent</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('dashboard')">订单任务</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('analytics')">分析报表</a>
                        </li>
                    </ul>
                    <div class="navbar-nav">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i> <span id="currentUser">客服 - 小王</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 任务仪表盘页面 -->
        <div id="dashboardPage" class="page-content active">
            <main class="container-fluid p-4">
                <h1 class="h3 mb-4">订单任务仪表盘</h1>

                <!-- 统计卡片 -->
                <div class="row g-4 mb-4">
                    <div class="col-md-4">
                        <div class="card stat-card border-left-primary shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-inbox text-primary"></i>
                                <div>
                                    <div class="text-xs fw-bold text-primary text-uppercase mb-1">待处理邮件</div>
                                    <div class="h5 mb-0 fw-bold text-gray-800">15</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stat-card border-left-warning shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-user-clock text-warning"></i>
                                <div>
                                    <div class="text-xs fw-bold text-warning text-uppercase mb-1">待人工审核</div>
                                    <div class="h5 mb-0 fw-bold text-gray-800">5</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card stat-card border-left-success shadow-sm">
                            <div class="card-body">
                                <i class="fas fa-check-circle text-success"></i>
                                <div>
                                    <div class="text-xs fw-bold text-success text-uppercase mb-1">今日已完成</div>
                                    <div class="h5 mb-0 fw-bold text-gray-800">128</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签页界面 -->
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <ul class="nav nav-tabs card-header-tabs">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#taskList">
                                    <i class="fas fa-list-ul me-2"></i>任务列表
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#routeReport">
                                    <i class="fas fa-route me-2"></i>线路聚合报表
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <!-- 任务列表 -->
                            <div class="tab-pane fade show active" id="taskList">
                                <p class="text-muted">实时处理AI分配的订单任务。</p>
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead class="table-light">
                                            <tr>
                                                <th>状态</th>
                                                <th>客户名称</th>
                                                <th>邮件主题</th>
                                                <th>接收时间</th>
                                                <th>综合置信度 (最低分)</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr onclick="showOrderReview()">
                                                <td><span class="badge bg-warning text-dark"><i class="fas fa-user-clock"></i> 待审核</span></td>
                                                <td>上海精密制造有限公司</td>
                                                <td>下单请求 - 20251027 - 批次A</td>
                                                <td>2025-10-27 10:15</td>
                                                <td>
                                                    <div class="confidence-cell" data-bs-toggle="tooltip" title="风险项: 地址(35%), 体积(0%)">
                                                        <div class="progress" style="height: 20px; width: 100%;">
                                                            <div class="progress-bar bg-danger" role="progressbar" style="width: 35%;">35%</div>
                                                        </div>
                                                        <i class="fas fa-map-marker-alt risk-icon"></i>
                                                        <i class="fas fa-cube risk-icon"></i>
                                                    </div>
                                                </td>
                                                <td><button class="btn btn-sm btn-primary" onclick="showOrderReview()">审核</button></td>
                                            </tr>
                                            <tr onclick="showOrderReview()">
                                                <td><span class="badge bg-warning text-dark"><i class="fas fa-user-clock"></i> 待审核</span></td>
                                                <td>深圳电子科技</td>
                                                <td>Fwd: 紧急发货需求</td>
                                                <td>2025-10-27 09:58</td>
                                                <td>
                                                    <div class="confidence-cell" data-bs-toggle="tooltip" title="风险项: 电话格式(50%)">
                                                        <div class="progress" style="height: 20px; width: 100%;">
                                                            <div class="progress-bar bg-warning text-dark" role="progressbar" style="width: 50%;">50%</div>
                                                        </div>
                                                        <i class="fas fa-phone-slash risk-icon"></i>
                                                    </div>
                                                </td>
                                                <td><button class="btn btn-sm btn-primary" onclick="showOrderReview()">审核</button></td>
                                            </tr>
                                            <tr onclick="showOrderReview()">
                                                <td><span class="badge bg-success"><i class="fas fa-check-circle"></i> 已处理</span></td>
                                                <td>苏州自动化设备</td>
                                                <td>常规补货订单 - SZ2025102701</td>
                                                <td>2025-10-27 09:30</td>
                                                <td>
                                                    <div class="confidence-cell" data-bs-toggle="tooltip" title="所有关键字段置信度均 > 95%">
                                                        <div class="progress" style="height: 20px; width: 100%;">
                                                            <div class="progress-bar bg-success" role="progressbar" style="width: 98%;">98%</div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><span class="badge bg-success">已完成</span></td>
                                            </tr>
                                            <tr onclick="showOrderReview()">
                                                <td><span class="badge bg-warning text-dark"><i class="fas fa-user-clock"></i> 待审核</span></td>
                                                <td>杭州物联科技</td>
                                                <td>Re: 配件紧急补充 - HZ-BJ-005</td>
                                                <td>2025-10-27 15:45</td>
                                                <td>
                                                    <div class="confidence-cell" data-bs-toggle="tooltip" title="风险项: 联系人信息(60%), 货物描述(40%)">
                                                        <div class="progress" style="height: 20px; width: 100%;">
                                                            <div class="progress-bar bg-warning text-dark" role="progressbar" style="width: 40%;">40%</div>
                                                        </div>
                                                        <i class="fas fa-user-xmark risk-icon"></i>
                                                        <i class="fas fa-box-open risk-icon"></i>
                                                    </div>
                                                </td>
                                                <td><button class="btn btn-sm btn-primary" onclick="showOrderReview()">审核</button></td>
                                            </tr>
                                            <tr onclick="showOrderReview()">
                                                <td><span class="badge bg-success"><i class="fas fa-check-circle"></i> 已处理</span></td>
                                                <td>青岛海运集团</td>
                                                <td>月度合同订单 - QD2025Q4-01</td>
                                                <td>2025-10-27 11:20</td>
                                                <td>
                                                    <div class="confidence-cell" data-bs-toggle="tooltip" title="合同客户，信息完整度高">
                                                        <div class="progress" style="height: 20px; width: 100%;">
                                                            <div class="progress-bar bg-success" role="progressbar" style="width: 96%;">96%</div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><span class="badge bg-success">已完成</span></td>
                                            </tr>
                                            <tr onclick="showOrderReview()">
                                                <td><span class="badge bg-warning text-dark"><i class="fas fa-user-clock"></i> 待审核</span></td>
                                                <td>成都电子制造</td>
                                                <td>Fw: 急件处理 - 明天要货</td>
                                                <td>2025-10-27 16:30</td>
                                                <td>
                                                    <div class="confidence-cell" data-bs-toggle="tooltip" title="风险项: 收货地址(20%), 时效要求(30%)">
                                                        <div class="progress" style="height: 20px; width: 100%;">
                                                            <div class="progress-bar bg-danger" role="progressbar" style="width: 20%;">20%</div>
                                                        </div>
                                                        <i class="fas fa-map-marker-alt risk-icon"></i>
                                                        <i class="fas fa-clock-rotate-left risk-icon"></i>
                                                    </div>
                                                </td>
                                                <td><button class="btn btn-sm btn-primary" onclick="showOrderReview()">审核</button></td>
                                            </tr>
                                            <tr onclick="showOrderReview()">
                                                <td><span class="badge bg-warning text-dark"><i class="fas fa-user-clock"></i> 待审核</span></td>
                                                <td>福州精密仪器</td>
                                                <td>设备运输询价及下单</td>
                                                <td>2025-10-27 13:15</td>
                                                <td>
                                                    <div class="confidence-cell" data-bs-toggle="tooltip" title="风险项: 价格信息(70%), 服务类型(55%)">
                                                        <div class="progress" style="height: 20px; width: 100%;">
                                                            <div class="progress-bar bg-warning text-dark" role="progressbar" style="width: 55%;">55%</div>
                                                        </div>
                                                        <i class="fas fa-dollar-sign risk-icon"></i>
                                                        <i class="fas fa-truck-field risk-icon"></i>
                                                    </div>
                                                </td>
                                                <td><button class="btn btn-sm btn-primary" onclick="showOrderReview()">审核</button></td>
                                            </tr>
                                            <tr onclick="showOrderReview()">
                                                <td><span class="badge bg-success"><i class="fas fa-check-circle"></i> 已处理</span></td>
                                                <td>天津汽配供应链</td>
                                                <td>周期性配送订单 - Week43</td>
                                                <td>2025-10-27 08:30</td>
                                                <td>
                                                    <div class="confidence-cell" data-bs-toggle="tooltip" title="老客户标准订单格式">
                                                        <div class="progress" style="height: 20px; width: 100%;">
                                                            <div class="progress-bar bg-success" role="progressbar" style="width: 94%;">94%</div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><span class="badge bg-success">已完成</span></td>
                                            </tr>
                                            <tr onclick="showOrderReview()">
                                                <td><span class="badge bg-info text-white"><i class="fas fa-hourglass-half"></i> 处理中</span></td>
                                                <td>重庆智能制造</td>
                                                <td>多批次发货安排 - CQ1027001-003</td>
                                                <td>2025-10-27 14:50</td>
                                                <td>
                                                    <div class="confidence-cell" data-bs-toggle="tooltip" title="风险项: 批次信息(75%), 时间安排(65%)">
                                                        <div class="progress" style="height: 20px; width: 100%;">
                                                            <div class="progress-bar bg-warning text-dark" role="progressbar" style="width: 65%;">65%</div>
                                                        </div>
                                                        <i class="fas fa-layer-group risk-icon"></i>
                                                        <i class="fas fa-calendar-xmark risk-icon"></i>
                                                    </div>
                                                </td>
                                                <td><button class="btn btn-sm btn-secondary">处理中</button></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 线路聚合报表 -->
                            <div class="tab-pane fade" id="routeReport">
                                <p class="text-muted">筛选可合并线路的订单，提前规划运力，降低成本。支持导出。</p>
                                
                                <!-- 筛选表单 -->
                                <div class="bg-light p-3 rounded mb-4 border">
                                    <form class="row g-3 align-items-end">
                                        <div class="col-md-3">
                                            <div class="row">
                                                <div class="col-6">
                                                    <label class="form-label">始发地</label>
                                                    <select class="form-select">
                                                        <option value="">全部</option>
                                                        <option value="上海">上海</option>
                                                        <option value="北京">北京</option>
                                                        <option value="深圳">深圳</option>
                                                    </select>
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">目的地</label>
                                                    <select class="form-select">
                                                        <option value="">全部</option>
                                                        <option value="广州" selected>广州</option>
                                                        <option value="深圳">深圳</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">客户名称</label>
                                            <input type="text" class="form-control" placeholder="输入客户名称">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">发货日期范围</label>
                                            <div class="input-group">
                                                <input type="date" class="form-control">
                                                <span class="input-group-text">-</span>
                                                <input type="date" class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-primary" type="button"><i class="fas fa-search me-2"></i>查询</button>
                                            <button class="btn btn-success" type="button"><i class="fas fa-file-excel me-2"></i>导出</button>
                                        </div>
                                    </form>
                                </div>

                                <!-- 报表结果 -->
                                <h6>查询结果：<span class="fw-bold text-danger">上海 -> 广州白云区</span> | 订单总数量：<span class="fw-bold text-primary">3 笔</span></h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>客户订单号</th>
                                                <th>客户名称</th>
                                                <th>发货日期</th>
                                                <th>件数</th>
                                                <th>重量(kg)</th>
                                                <th>体积(m³)</th>
                                                <th>录入状态</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>C20251027A</td>
                                                <td>上海精密制造有限公司</td>
                                                <td>2025-10-28</td>
                                                <td>3</td>
                                                <td>50</td>
                                                <td>0.5</td>
                                                <td><span class="badge bg-success">已录入TMS</span></td>
                                            </tr>
                                            <tr>
                                                <td>PO-SH-1102</td>
                                                <td>上海电子元件厂</td>
                                                <td>2025-10-28</td>
                                                <td>12</td>
                                                <td>250</td>
                                                <td>1.8</td>
                                                <td><span class="badge bg-success">已录入TMS</span></td>
                                            </tr>
                                            <tr>
                                                <td>20251027-GZ-01</td>
                                                <td>上海艾科科技</td>
                                                <td>2025-10-28</td>
                                                <td>5</td>
                                                <td>88</td>
                                                <td>0.9</td>
                                                <td><span class="badge bg-warning text-dark">待审核</span></td>
                                            </tr>
                                        </tbody>
                                        <tfoot>
                                            <tr class="fw-bold table-light">
                                                <td colspan="3">合计</td>
                                                <td>20 件</td>
                                                <td>388 kg</td>
                                                <td>3.2 m³</td>
                                                <td></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- 订单审核页面 -->
        <div id="orderReviewPage" class="page-content">
            <div class="review-container">
                <header class="bg-light p-3 border-bottom">
                     <nav aria-label="breadcrumb">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="#" onclick="showPage('dashboard')">订单任务</a></li>
                            <li class="breadcrumb-item active" aria-current="page">审核订单 #ORD-1015</li>
                        </ol>
                    </nav>
                </header>

                <main class="review-content container-fluid p-4">
                    <div class="row g-4 h-100">
                        <!-- Left Pane: Original Email + AI Log -->
                        <div class="col-lg-5 h-100 d-flex flex-column">
                            <div class="card card-scrollable mb-4">
                                <div class="card-header fw-bold"><i class="fas fa-envelope-open-text"></i> 原始邮件内容</div>
                                <div class="card-body card-body-scrollable">
                                    <p><strong>发件人:</strong> <EMAIL></p>
                                    <p><strong>主题:</strong> 下单请求 - PO: C20251027A</p>
                                    <hr>
                                    <p>Hi 客服，</p>
                                    <p>请安排发货，信息如下：</p>
                                    <ul>
                                        <li><strong>发货方：</strong>上海精密制造有限公司</li>
                                        <li><strong>收货方：</strong>广州白云电器</li>
                                        <li><strong>收货人：</strong>李经理</li>
                                        <li><strong>收货电话：</strong>13800138000</li>
                                        <li><strong>收货地址：</strong>广州市白云区江高镇</li>
                                        <li><strong>货物：</strong>电子元器件，3件，总重50kg</li>
                                        <li><strong>发货日期：</strong>10月27日</li>
                                        <li><strong>订单号：</strong>C20251027A</li>
                                    </ul>
                                    <p>请尽快安排，谢谢！</p>
                                    <br>
                                    <p>张伟<br>
                                    上海精密制造有限公司<br>
                                    电话：021-12345678</p>
                                    <div class="mt-4">
                                        <h6><i class="fas fa-paperclip"></i> 附件 (1)</h6>
                                        <a href="#" class="btn btn-outline-secondary btn-sm"><i class="fas fa-file-pdf"></i> 装箱单-C20251027A.pdf</a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- NEW: AI Processing Log -->
                            <div class="accordion" id="aiLogAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingOne">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                            <i class="fas fa-stream me-2"></i> <strong>AI处理日志 (思维链)</strong>
                                        </button>
                                    </h2>
                                    <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#aiLogAccordion">
                                        <div class="accordion-body">
                                            <div class="log-item">
                                                <div class="log-icon"><i class="fas fa-inbox text-success"></i></div>
                                                <div class="log-content">
                                                    <div class="log-title">读取邮件 <span class="badge bg-success float-end">成功</span></div>
                                                    <div class="log-detail">已接收来自 <EMAIL> 的新邮件。</div>
                                                </div>
                                            </div>
                                            <div class="log-item">
                                                <div class="log-icon"><i class="fas fa-file-pdf text-success"></i></div>
                                                <div class="log-content">
                                                    <div class="log-title">调用 [AI文档解析工具] <span class="badge bg-success float-end">成功</span></div>
                                                    <div class="log-detail">已解析附件 `装箱单-C20251027A.pdf`。</div>
                                                </div>
                                            </div>
                                            <div class="log-item">
                                                <div class="log-icon"><i class="fas fa-magic-wand-sparkles text-success"></i></div>
                                                <div class="log-content">
                                                    <div class="log-title">提取关键字段 <span class="badge bg-success float-end">成功</span></div>
                                                    <div class="log-detail">从邮件正文和附件中共提取到 11 个关键字段。</div>
                                                </div>
                                            </div>
                                            <div class="log-item">
                                                <div class="log-icon"><i class="fas fa-map-marker-alt text-warning"></i></div>
                                                <div class="log-content">
                                                    <div class="log-title">调用 [GIS地址校验工具] <span class="badge bg-warning text-dark float-end">警告</span></div>
                                                    <div class="log-detail">地址 '广州市白云区江高镇' 识别为有效区域，但缺乏详细路名，被标记为"模糊地址"。</div>
                                                </div>
                                            </div>
                                             <div class="log-item">
                                                <div class="log-icon"><i class="fas fa-cube text-warning"></i></div>
                                                <div class="log-content">
                                                    <div class="log-title">检查必填项：体积 <span class="badge bg-warning text-dark float-end">警告</span></div>
                                                    <div class="log-detail">在所有源文档中均未找到 '体积' 或 'Volume' 等相关字段。</div>
                                                </div>
                                            </div>
                                            <div class="log-item">
                                                <div class="log-icon"><i class="fas fa-user-clock text-primary"></i></div>
                                                <div class="log-content">
                                                    <div class="log-title">最终决策 <span class="badge bg-primary float-end">转人工</span></div>
                                                    <div class="log-detail">综合置信度 85% (低于 95% 阈值)，已将任务分配至人工审核队列。</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Pane: AI Parsed Form (Unchanged from previous version) -->
                        <div class="col-lg-7 h-100 d-flex flex-column">
                            <div class="card card-scrollable">
                                <div class="card-header fw-bold"><i class="fas fa-cogs"></i> AI解析与订单表单</div>
                                <div class="card-body card-body-scrollable pb-0">
                                    <!-- The full form from the previous response goes here -->
                                     <form class="row g-3 mb-3">
                                        <h5 class="form-section-title">发货方信息</h5>
                                        <div class="col-md-6">
                                            <label class="form-label">发货人/公司</label>
                                            <div class="field-confidence">
                                                <input type="text" class="form-control" value="上海精密制造有限公司" readonly>
                                                <span class="confidence-badge confidence-high">95%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">发货联系人</label>
                                            <div class="field-confidence">
                                                <input type="text" class="form-control" value="王强">
                                                <span class="confidence-badge confidence-medium">78%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">发货电话</label>
                                            <div class="field-confidence">
                                                <input type="tel" class="form-control" value="021-58886666">
                                                <span class="confidence-badge confidence-high">92%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">发货地址</label>
                                            <div class="field-confidence">
                                                <input type="text" class="form-control" value="上海市浦东新区金桥路100号" readonly>
                                                <span class="confidence-badge confidence-high">98%</span>
                                            </div>
                                        </div>
                                        <h5 class="form-section-title">收货方信息</h5>
                                        <div class="col-md-6">
                                            <label class="form-label">收货人/公司</label>
                                            <div class="field-confidence">
                                                <input type="text" class="form-control" value="广州白云电器">
                                                <span class="confidence-badge confidence-high">88%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">收货联系人</label>
                                            <div class="field-confidence">
                                                <input type="text" class="form-control" value="李经理">
                                                <span class="confidence-badge confidence-medium">75%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">收货电话</label>
                                            <div class="field-confidence">
                                                <input type="tel" class="form-control" value="13800138000">
                                                <span class="confidence-badge confidence-high">95%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">收货地址</label>
                                            <div class="field-confidence">
                                                <input type="text" class="form-control highlight-warning" value="广州市白云区江高镇">
                                                <span class="confidence-badge confidence-low">35%</span>
                                            </div>
                                            <div class="form-text ai-tip"><i class="fas fa-lightbulb text-warning"></i> AI提示：地址不够详细，建议补充路名和门牌号。</div>
                                        </div>
                                        <h5 class="form-section-title">货物与服务信息</h5>
                                        <div class="col-md-4">
                                            <label class="form-label">订单号 (客户侧)</label>
                                            <div class="field-confidence">
                                                <input type="text" class="form-control" value="C20251027A">
                                                <span class="confidence-badge confidence-high">98%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">发货日期</label>
                                            <div class="field-confidence">
                                                <input type="date" class="form-control" value="2025-10-27">
                                                <span class="confidence-badge confidence-high">90%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">服务类型</label>
                                            <div class="field-confidence">
                                                <select class="form-select"><option>普货零担</option><option>普货整车</option><option>冷链运输</option></select>
                                                <span class="confidence-badge confidence-medium">68%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">货物名称</label>
                                            <div class="field-confidence">
                                                <input type="text" class="form-control" value="电子元器件">
                                                <span class="confidence-badge confidence-high">85%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">件数</label>
                                            <div class="field-confidence">
                                                <input type="number" class="form-control" value="3">
                                                <span class="confidence-badge confidence-high">95%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">重量(kg)</label>
                                            <div class="field-confidence">
                                                <input type="number" class="form-control" value="50">
                                                <span class="confidence-badge confidence-high">92%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">体积(m³)</label>
                                            <div class="field-confidence">
                                                <input type="number" class="form-control highlight-warning" placeholder="请输入">
                                                <span class="confidence-badge confidence-missing">0%</span>
                                            </div>
                                            <div class="form-text ai-tip"><i class="fas fa-lightbulb text-warning"></i> AI提示：邮件中未找到体积信息。</div>
                                        </div>
                                    </form>
                                </div>
                                
                                <!-- Chatbot Section -->
                                <div class="chatbot-container">
                                     <h6 class="mb-3 text-muted"><i class="fas fa-comments"></i> AI指令与澄清控制台</h6>
                                    <div class="chat-history">
                                        <!-- AI Message with Buttons -->
                                        <div class="chat-message ai-message">
                                            <div class="avatar"><i class="fas fa-robot"></i></div>
                                            <div class="message-content">
                                                <p class="mb-2">根据地址 '广州市白云区江高镇'，我查询到该客户历史常用地址为：<br><strong>广州市白云区江高镇神山大道西388号</strong></p>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-outline-success btn-sm"><i class="fas fa-check"></i> 使用此地址</button>
                                                    <button type="button" class="btn btn-outline-secondary btn-sm"><i class="fas fa-times"></i> 保持原样</button>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- User Message -->
                                        <div class="chat-message user-message">
                                            <div class="message-content">
                                                查一下这个客户的标准SLA是什么？
                                            </div>
                                             <div class="avatar ms-2 bg-primary text-white"><i class="fas fa-user"></i></div>
                                        </div>
                                        <!-- AI Response -->
                                        <div class="chat-message ai-message">
                                            <div class="avatar"><i class="fas fa-robot"></i></div>
                                            <div class="message-content">
                                                好的。该客户（上海精密制造有限公司）为VIP客户，其标准SLA为：珠三角、长三角地区24小时内送达，其他主要城市48小时内送达。
                                            </div>
                                        </div>
                                    </div>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="在此输入指令或提问，如"拆分订单"...">
                                        <button class="btn btn-primary" type="button"><i class="fas fa-paper-plane"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

                <footer class="bg-light p-3 border-top d-flex flex-wrap justify-content-between align-items-center gap-2">
                    <button class="btn btn-outline-danger">
                        <i class="fas fa-times"></i> <span class="d-none d-md-inline">标记为无效订单</span>
                    </button>
                    <div class="d-flex flex-wrap gap-2">
                        <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#askCustomerModal">
                            <i class="fas fa-envelope"></i> <span class="d-none d-md-inline">邮件追问</span>
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-save"></i> <span class="d-none d-md-inline">保存修改</span>
                        </button>
                        <button class="btn btn-success btn-lg" onclick="confirmOrder()">
                            <i class="fas fa-check"></i> <span class="d-none d-sm-inline">确认下单</span>
                        </button>
                    </div>
                </footer>
            </div>
        </div>

        <!-- 分析报表页面 -->
        <div id="analyticsPage" class="page-content">
            <main class="container-fluid p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 m-0">主管分析报表</h1>
                    <!-- Date range filter can be moved to the new section for unified control -->
                </div>
                
                <!-- KPI Cards (Unchanged) -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6"><div class="card text-center kpi-card shadow-sm"><div class="card-body"><p class="card-text text-uppercase text-muted">订单录入自动化率</p><p class="display-4 text-success">72%</p></div></div></div>
                    <div class="col-lg-3 col-md-6"><div class="card text-center kpi-card shadow-sm"><div class="card-body"><p class="card-text text-uppercase text-muted">单均处理时长</p><p class="display-4 text-primary">45<small class="h4">秒</small></p></div></div></div>
                    <div class="col-lg-3 col-md-6"><div class="card text-center kpi-card shadow-sm"><div class="card-body"><p class="card-text text-uppercase text-muted">录入错误率降低</p><p class="display-4 text-info">96%</p></div></div></div>
                    <div class="col-lg-3 col-md-6"><div class="card text-center kpi-card shadow-sm"><div class="card-body"><p class="card-text text-uppercase text-muted">今日未处理</p><p class="display-4 text-danger">15</p></div></div></div>
                </div>

                <!-- Charts (Unchanged) -->
                <div class="row g-4 mb-5">
                     <div class="col-lg-12"><div class="card shadow-sm"><div class="card-header fw-bold">团队订单处理趋势 (近7日)</div><div class="card-body"><div class="chart-placeholder"><i class="fas fa-chart-bar fa-3x"></i><p class="ms-3">Bar Chart: Daily Order Volume by Team</p></div></div></div></div>
                </div>

                <!-- NEW: Comprehensive Order Query & Management Module -->
                <div class="card shadow-sm">
                    <div class="card-header bg-dark text-white">
                        <h5 class="m-0 fw-bold"><i class="fas fa-search-location me-2"></i>综合订单查询与管理</h5>
                    </div>
                    <div class="card-body">
                        <!-- Filter Form -->
                        <div class="bg-light p-3 rounded mb-4 border">
                            <form class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">始发地</label>
                                    <input type="text" class="form-control" placeholder="如: 上海">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">目的地</label>
                                    <input type="text" class="form-control" placeholder="如: 广州白云区">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">客户名称</label>
                                    <input type="text" class="form-control" placeholder="支持模糊搜索">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">客服人员</label>
                                    <select class="form-select">
                                        <option selected>所有客服</option>
                                        <option>小王</option>
                                        <option>小李</option>
                                        <option>小张</option>
                                    </select>
                                </div>
                                 <div class="col-md-6">
                                    <label class="form-label">发货日期范围</label>
                                    <div class="input-group">
                                        <input type="date" class="form-control" value="2025-10-27">
                                        <span class="input-group-text">-</span>
                                        <input type="date" class="form-control" value="2025-10-27">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">订单状态</label>
                                    <select class="form-select">
                                        <option selected>所有状态</option>
                                        <option>待审核</option>
                                        <option>已完成</option>
                                        <option>处理失败</option>
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button class="btn btn-primary w-100 me-2" type="button"><i class="fas fa-search me-1"></i> 查询</button>
                                    <button class="btn btn-success w-100" type="button"><i class="fas fa-file-excel me-1"></i> 导出</button>
                                </div>
                            </form>
                        </div>

                        <!-- Query Summary -->
                        <div class="alert alert-info d-flex justify-content-between align-items-center">
                            <span>查询结果：共 <strong class="mx-1">128</strong> 条订单记录。</span>
                            <span>
                                <span class="badge bg-success me-2">已完成: 108</span>
                                <span class="badge bg-warning text-dark me-2">待审核: 15</span>
                                <span class="badge bg-danger">处理失败: 5</span>
                            </span>
                        </div>

                        <!-- Results Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>状态</th>
                                        <th>处理客服</th>
                                        <th>客户名称</th>
                                        <th>线路</th>
                                        <th>发货日期</th>
                                        <th>货物详情 (件/重/体)</th>
                                        <th>客户订单号</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                                                            <tr>
                                            <td><span class="badge bg-warning text-dark">待审核</span></td>
                                            <td>小王</td>
                                            <td>上海精密制造</td>
                                            <td>上海 -> 广州</td>
                                            <td>2025-10-28</td>
                                            <td>3 / 50kg / 0.5m³</td>
                                            <td>C20251027A</td>
                                        <td><a href="#" class="btn btn-sm btn-outline-primary" onclick="showOrderReview()">查看</a></td>
                                    </tr>
                                                                            <tr>
                                            <td><span class="badge bg-success">已完成</span></td>
                                            <td>小李</td>
                                            <td>苏州自动化设备</td>
                                            <td>苏州 -> 深圳</td>
                                            <td>2025-10-27</td>
                                            <td>10 / 320kg / 2.1m³</td>
                                            <td>SZ2025102701</td>
                                        <td><a href="#" class="btn btn-sm btn-outline-primary">查看</a></td>
                                    </tr>
                                                                            <tr>
                                            <td><span class="badge bg-danger">失败</span></td>
                                            <td>小张</td>
                                            <td>杭州物联</td>
                                            <td>杭州 -> 北京</td>
                                            <td>2025-10-27</td>
                                        <td>1 / 15kg / 0.1m³</td>
                                        <td>HZ-BJ-005</td>
                                        <td><a href="#" class="btn btn-sm btn-outline-primary">查看</a></td>
                                    </tr>
                                                                              <tr>
                                            <td><span class="badge bg-success">已完成</span></td>
                                            <td>小王</td>
                                            <td>上海艾科科技</td>
                                            <td>上海 -> 广州</td>
                                            <td>2025-10-28</td>
                                            <td>5 / 88kg / 0.9m³</td>
                                            <td>20251027-GZ-01</td>
                                        <td><a href="#" class="btn btn-sm btn-outline-primary">查看</a></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                         <nav class="mt-3">
                            <ul class="pagination justify-content-end">
                                <li class="page-item disabled"><a class="page-link" href="#">上一页</a></li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item"><a class="page-link" href="#">下一页</a></li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 澄清邮件模态框 -->
    <div class="modal fade" id="askCustomerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-envelope"></i> 发送澄清邮件
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label class="form-label">收件人</label>
                            <input type="email" class="form-control" value="<EMAIL>" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">主题</label>
                            <input type="text" class="form-control" value="Re: 下单请求 - PO: C20251027A [需要补充信息]">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮件内容</label>
                            <textarea class="form-control" rows="6">您好 张伟，

感谢您的订单 C20251027A。为了更好地为您服务，我们需要确认以下信息：

1. 收货地址不够详细，请提供完整的街道地址和门牌号
2. 请确认货物的体积信息（长宽高或立方米）

请您尽快回复，以便我们安排发货。

谢谢！

Uniner Agent</textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> 发送邮件
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面切换功能
        function showPage(pageName) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示指定页面
            document.getElementById(pageName + 'Page').classList.add('active');
            
            // 更新导航栏状态
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // 更新用户显示
            if (pageName === 'analytics') {
                document.getElementById('currentUser').textContent = '运营主管 - 余总';
            } else {
                document.getElementById('currentUser').textContent = '客服 - 小王';
            }
        }

        // 显示订单审核页面
        function showOrderReview() {
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            document.getElementById('orderReviewPage').classList.add('active');
        }

        // 登录功能
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            document.getElementById('loginPage').classList.remove('active');
            document.getElementById('mainApp').classList.add('active');
        });

        // 退出登录
        function logout() {
            document.getElementById('mainApp').classList.remove('active');
            document.getElementById('loginPage').classList.add('active');
            // 重置表单
            document.getElementById('loginForm').reset();
        }

        // 确认下单
        function confirmOrder() {
            alert('订单确认成功！已发送至TMS系统。');
            showPage('dashboard');
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示登录页面，不需要额外操作，因为登录页面已经有 active 类
            
            // 初始化Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            })
        });
    </script>
</body>
</html> 