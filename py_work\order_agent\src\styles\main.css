/* ==========================================================================
   订单录入智能体系统 - 主样式文件
   ========================================================================== */

/* CSS Custom Properties (CSS Variables) */
:root {
  /* Colors */
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #0dcaf0;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  
  /* Background Colors */
  --bg-primary: #f8f9fa;
  --bg-card: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* Text Colors */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #6c757d;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Z-index layers */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ==========================================================================
   Base Styles
   ========================================================================== */

* {
  box-sizing: border-box;
}

body {
  background-color: var(--bg-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
}

/* ==========================================================================
   Loading Overlay
   ========================================================================== */

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-toast);
  transition: opacity var(--transition-normal);
}

.loading-overlay.hidden {
  opacity: 0;
  pointer-events: none;
}

/* ==========================================================================
   App Container
   ========================================================================== */

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ==========================================================================
   Page Management
   ========================================================================== */

.page-content {
  display: none;
  animation: fadeIn var(--transition-normal);
}

.page-content.active {
  display: block;
}

.main-app {
  display: none;
  flex: 1;
}

.main-app.active {
  display: flex;
  flex-direction: column;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==========================================================================
   Import Component Styles
   ========================================================================== */

/* Login Page Styles */
@import url('pages/login.css');

/* Dashboard Page Styles */
@import url('pages/dashboard.css');

/* Order Review Page Styles */
@import url('pages/order-review.css');

/* Analytics Page Styles */
@import url('pages/analytics.css');

/* Component Styles */
@import url('components/navbar.css');
@import url('components/stat-card.css');
@import url('components/forms.css');
@import url('components/tables.css');
@import url('components/modals.css');
@import url('components/chat.css');

/* Utility Styles */
@import url('utilities/responsive.css');
@import url('utilities/helpers.css');

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
  .navbar,
  .btn,
  .modal,
  .toast-container {
    display: none !important;
  }
  
  .page-content {
    display: block !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}
